using Robot.Core.Entities;
using Robot.Core.Enums;

namespace Robot.Core.Interfaces;

/// <summary>
/// 投注服务接口
/// 处理所有投注相关的业务逻辑
/// </summary>
public interface IBetService
{
    /// <summary>
    /// 处理投注请求
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="betContent">投注内容</param>
    /// <param name="betLottery">投注彩种</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>投注结果</returns>
    Task<BetResult> ProcessBetAsync(
        string memberAccount, 
        string betContent, 
        EnumBetLottery betLottery, 
        long messageId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 自动飞单
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>飞单结果</returns>
    Task<AutoBetResult> AutoBetAsync(string issue, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查飞单结果
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>检查结果</returns>
    Task<BetCheckResult> CheckBetResultAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 撤销投注
    /// </summary>
    /// <param name="betOrderId">投注订单ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>撤销结果</returns>
    Task<CancelBetResult> CancelBetAsync(long betOrderId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 自动撤销失败的投注
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>撤销结果</returns>
    Task<AutoCancelResult> AutoCancelFailedBetsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 结算投注数据
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="drawResult">开奖结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>结算结果</returns>
    Task<SettlementResult> SettleBetDataAsync(
        string issue, 
        KaiJiang drawResult, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取投注统计
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>投注统计</returns>
    Task<BetStatistics> GetBetStatisticsAsync(string issue, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证投注内容
    /// </summary>
    /// <param name="betContent">投注内容</param>
    /// <param name="betLottery">投注彩种</param>
    /// <returns>验证结果</returns>
    Task<BetValidationResult> ValidateBetContentAsync(string betContent, EnumBetLottery betLottery);
    
    /// <summary>
    /// 获取赔率信息
    /// </summary>
    /// <param name="betItem">投注项目</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>赔率信息</returns>
    Task<Odds?> GetOddsAsync(string betItem, CancellationToken cancellationToken = default);
}

/// <summary>
/// 投注结果
/// </summary>
public class BetResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<BetOrder> Orders { get; set; } = new();
    public decimal TotalAmount { get; set; }
}

/// <summary>
/// 自动飞单结果
/// </summary>
public class AutoBetResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int ProcessedCount { get; set; }
    public decimal TotalAmount { get; set; }
}

/// <summary>
/// 投注检查结果
/// </summary>
public class BetCheckResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int SuccessCount { get; set; }
    public int FailedCount { get; set; }
}

/// <summary>
/// 撤销投注结果
/// </summary>
public class CancelBetResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public decimal RefundAmount { get; set; }
}

/// <summary>
/// 自动撤销结果
/// </summary>
public class AutoCancelResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int CancelledCount { get; set; }
    public decimal TotalRefundAmount { get; set; }
}

/// <summary>
/// 结算结果
/// </summary>
public class SettlementResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int SettledCount { get; set; }
    public decimal TotalWinAmount { get; set; }
    public decimal TotalLoseAmount { get; set; }
}

/// <summary>
/// 投注统计
/// </summary>
public class BetStatistics
{
    public string Issue { get; set; } = string.Empty;
    public int TotalBets { get; set; }
    public decimal TotalAmount { get; set; }
    public Dictionary<string, decimal> BetsByItem { get; set; } = new();
    public Dictionary<string, int> BetCountByItem { get; set; } = new();
}

/// <summary>
/// 投注验证结果
/// </summary>
public class BetValidationResult
{
    public bool IsValid { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public List<BetInfo> ParsedBets { get; set; } = new();
}
