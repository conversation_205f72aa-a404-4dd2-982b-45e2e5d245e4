using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Serilog;
using Robot.Core.Interfaces;
using Robot.Infrastructure.Data;
using Robot.Infrastructure.ChatPlatforms;
using Robot.Core.Services;
using Robot.Application.Handlers;
using MediatR;
using AutoMapper;
using FluentValidation;
using System.Reflection;

namespace Robot.Infrastructure.Configuration;

/// <summary>
/// 依赖注入配置类
/// 负责注册所有服务和依赖关系
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// 配置所有服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    /// <returns>配置后的服务集合</returns>
    public static IServiceCollection ConfigureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 配置日志
        ConfigureLogging(services);
        
        // 配置数据访问
        ConfigureDataAccess(services);
        
        // 配置业务服务
        ConfigureBusinessServices(services);
        
        // 配置基础设施服务
        ConfigureInfrastructureServices(services);
        
        // 配置应用服务
        ConfigureApplicationServices(services);
        
        // 配置AutoMapper
        ConfigureAutoMapper(services);
        
        // 配置MediatR
        ConfigureMediatR(services);
        
        // 配置验证器
        ConfigureValidators(services);
        
        return services;
    }
    
    /// <summary>
    /// 配置日志服务
    /// </summary>
    private static void ConfigureLogging(IServiceCollection services)
    {
        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.File("logs/robot-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();
            
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog();
        });
    }
    
    /// <summary>
    /// 配置数据访问服务
    /// </summary>
    private static void ConfigureDataAccess(IServiceCollection services)
    {
        // 注册FreeSql实例
        services.AddSingleton<IFreeSql>(provider =>
        {
            return new FreeSqlBuilder()
                .UseConnectionString(FreeSql.DataType.Sqlite, 
                    @"Data Source=Robot.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;")
                .UseAutoSyncStructure(true)
                .Build();
        });
        
        // 注册Repository
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped<IUnitOfWork, UnitOfWork>();
    }
    
    /// <summary>
    /// 配置业务服务
    /// </summary>
    private static void ConfigureBusinessServices(IServiceCollection services)
    {
        services.AddScoped<IBetService, BetService>();
        services.AddScoped<IDrawService, DrawService>();
        services.AddScoped<IFinanceService, FinanceService>();
        services.AddScoped<IMemberService, MemberService>();
        services.AddScoped<IRobotService, RobotService>();
    }
    
    /// <summary>
    /// 配置基础设施服务
    /// </summary>
    private static void ConfigureInfrastructureServices(IServiceCollection services)
    {
        services.AddScoped<IChatPlatformFactory, ChatPlatformFactory>();
        services.AddScoped<IConfigurationService, ConfigurationService>();
        services.AddScoped<IImageService, ImageService>();
        services.AddScoped<IHttpService, HttpService>();
    }
    
    /// <summary>
    /// 配置应用服务
    /// </summary>
    private static void ConfigureApplicationServices(IServiceCollection services)
    {
        // 注册所有命令和查询处理器
        var assembly = Assembly.GetExecutingAssembly();
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(assembly));
    }
    
    /// <summary>
    /// 配置AutoMapper
    /// </summary>
    private static void ConfigureAutoMapper(IServiceCollection services)
    {
        services.AddAutoMapper(Assembly.GetExecutingAssembly());
    }
    
    /// <summary>
    /// 配置MediatR
    /// </summary>
    private static void ConfigureMediatR(IServiceCollection services)
    {
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
    }
    
    /// <summary>
    /// 配置验证器
    /// </summary>
    private static void ConfigureValidators(IServiceCollection services)
    {
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
