using Robot.Core.Entities;

namespace Robot.Core.Interfaces;

/// <summary>
/// 财务服务接口
/// 处理所有财务相关的业务逻辑
/// </summary>
public interface IFinanceService
{
    /// <summary>
    /// 处理上分申请
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="amount">金额</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<AddMoneyResult> ProcessAddMoneyAsync(
        string memberAccount, 
        decimal amount, 
        string messageId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理下分申请
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="amount">金额</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<SubMoneyResult> ProcessSubMoneyAsync(
        string memberAccount, 
        decimal amount, 
        string messageId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 审核上分申请
    /// </summary>
    /// <param name="addMoneyId">上分申请ID</param>
    /// <param name="approved">是否批准</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>审核结果</returns>
    Task<ApprovalResult> ApproveAddMoneyAsync(
        long addMoneyId, 
        bool approved,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 审核下分申请
    /// </summary>
    /// <param name="subMoneyId">下分申请ID</param>
    /// <param name="approved">是否批准</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>审核结果</returns>
    Task<ApprovalResult> ApproveSubMoneyAsync(
        long subMoneyId, 
        bool approved,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理回水
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>回水结果</returns>
    Task<RebateResult> ProcessRebateAsync(
        string memberAccount, 
        long messageId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 一键回水
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>回水结果</returns>
    Task<OneKeyRebateResult> OneKeyRebateAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取会员余额
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>余额信息</returns>
    Task<decimal> GetMemberBalanceAsync(string memberAccount, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新会员余额
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="amount">变动金额</param>
    /// <param name="reason">变动原因</param>
    /// <param name="reference">关联信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<BalanceUpdateResult> UpdateMemberBalanceAsync(
        string memberAccount, 
        decimal amount, 
        string reason, 
        string reference,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取财务记录
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>财务记录</returns>
    Task<List<Finance>> GetFinanceRecordsAsync(
        string memberAccount, 
        DateTime? startDate = null, 
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 上分结果
/// </summary>
public class AddMoneyResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public long RequestId { get; set; }
    public decimal Amount { get; set; }
}

/// <summary>
/// 下分结果
/// </summary>
public class SubMoneyResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public long RequestId { get; set; }
    public decimal Amount { get; set; }
}

/// <summary>
/// 审核结果
/// </summary>
public class ApprovalResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public decimal ProcessedAmount { get; set; }
    public decimal NewBalance { get; set; }
}

/// <summary>
/// 回水结果
/// </summary>
public class RebateResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public decimal RebateAmount { get; set; }
    public decimal NewBalance { get; set; }
    public int ProcessedOrders { get; set; }
}

/// <summary>
/// 一键回水结果
/// </summary>
public class OneKeyRebateResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int ProcessedMembers { get; set; }
    public decimal TotalRebateAmount { get; set; }
    public Dictionary<string, decimal> MemberRebates { get; set; } = new();
}

/// <summary>
/// 余额更新结果
/// </summary>
public class BalanceUpdateResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public decimal OldBalance { get; set; }
    public decimal NewBalance { get; set; }
    public decimal ChangeAmount { get; set; }
}
