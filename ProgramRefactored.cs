using System.Net;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Serilog;
using Robot.Infrastructure.Configuration;
using Robot.Core.Interfaces;
using Robot.Ui;

namespace Robot;

/// <summary>
/// 重构后的程序入口点
/// 使用依赖注入和现代化架构
/// </summary>
internal static class ProgramRefactored
{
    private static IServiceProvider? _serviceProvider;
    private static IHost? _host;

    /// <summary>
    /// 应用程序的主入口点
    /// </summary>
    [STAThread]
    static async Task Main()
    {
        try
        {
            // 初始化应用程序配置
            ApplicationConfiguration.Initialize();

            // 配置和构建主机
            _host = CreateHostBuilder().Build();
            _serviceProvider = _host.Services;

            // 启动主机服务
            await _host.StartAsync();

            // 初始化数据库
            await InitializeDatabaseAsync();

            // 检查单实例运行
            if (!CheckSingleInstance())
            {
                MessageBox.Show(@"KingRobot已经在运行，请勿重复打开！");
                return;
            }

            // 配置网络设置
            ConfigureNetworkSettings();

            // 检查更新
            await CheckForUpdatesAsync();

            // 启动应用程序
            await RunApplicationAsync();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
            MessageBox.Show($"应用程序启动失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 清理资源
            await DisposeAsync();
        }
    }

    /// <summary>
    /// 创建主机构建器
    /// </summary>
    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", 
                    optional: true, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // 注册所有服务
                services.ConfigureServices(context.Configuration);
            })
            .UseSerilog((context, services, configuration) =>
            {
                configuration
                    .ReadFrom.Configuration(context.Configuration)
                    .ReadFrom.Services(services)
                    .Enrich.FromLogContext()
                    .WriteTo.Console()
                    .WriteTo.File("logs/robot-.txt", rollingInterval: RollingInterval.Day);
            });
    }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    private static async Task InitializeDatabaseAsync()
    {
        try
        {
            var logger = _serviceProvider?.GetService<ILogger<Program>>();
            logger?.LogInformation("正在初始化数据库...");

            // 这里可以添加数据库初始化逻辑
            // 例如：创建表、初始化数据等

            logger?.LogInformation("数据库初始化完成");
        }
        catch (Exception ex)
        {
            Log.Error(ex, "数据库初始化失败");
            throw;
        }
    }

    /// <summary>
    /// 检查单实例运行
    /// </summary>
    private static bool CheckSingleInstance()
    {
        // 这里可以实现单实例检查逻辑
        // 暂时返回true，后续可以集成原有的SingleInstance逻辑
        return true;
    }

    /// <summary>
    /// 配置网络设置
    /// </summary>
    private static void ConfigureNetworkSettings()
    {
        ServicePointManager.Expect100Continue = false;
        ServicePointManager.SecurityProtocol = SecurityProtocolType.SystemDefault;
    }

    /// <summary>
    /// 检查更新
    /// </summary>
    private static async Task CheckForUpdatesAsync()
    {
        try
        {
            var logger = _serviceProvider?.GetService<ILogger<Program>>();
            logger?.LogInformation("检查应用程序更新...");

            // 这里可以集成原有的自动更新逻辑
            // 暂时跳过更新检查

            logger?.LogInformation("更新检查完成");
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "更新检查失败");
        }
    }

    /// <summary>
    /// 运行应用程序
    /// </summary>
    private static async Task RunApplicationAsync()
    {
        try
        {
            var logger = _serviceProvider?.GetService<ILogger<Program>>();
            logger?.LogInformation("启动应用程序界面...");

            // 显示登录窗口
            var formIndex = new FormIndex();
            formIndex.ShowDialog();

            // 如果登录成功，显示主窗体
            if (formIndex.DialogResult == DialogResult.OK)
            {
                // 使用依赖注入创建主窗体
                var mainForm = _serviceProvider?.GetService<FormMain>() ?? new FormMain();
                Application.Run(mainForm);
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "应用程序运行失败");
            throw;
        }
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    private static async Task DisposeAsync()
    {
        try
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            Log.CloseAndFlush();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"资源清理失败：{ex.Message}");
        }
    }

    /// <summary>
    /// 获取服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    public static T? GetService<T>() where T : class
    {
        return _serviceProvider?.GetService<T>();
    }

    /// <summary>
    /// 获取必需的服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    public static T GetRequiredService<T>() where T : class
    {
        return _serviceProvider?.GetRequiredService<T>() 
            ?? throw new InvalidOperationException($"服务 {typeof(T).Name} 未注册");
    }
}
