using Robot.Core.Entities;
using Robot.Core.Enums;

namespace Robot.Core.Interfaces;

/// <summary>
/// 开奖服务接口
/// 处理开奖相关的业务逻辑
/// </summary>
public interface IDrawService
{
    /// <summary>
    /// 获取开奖信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>获取结果</returns>
    Task<DrawInfoResult> GetDrawInfoAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理开奖信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<ProcessDrawResult> ProcessDrawInfoAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 生成开奖图片
    /// </summary>
    /// <param name="drawData">开奖数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>图片生成结果</returns>
    Task<ImageGenerationResult> GenerateDrawImageAsync(KaiJiang drawData, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 生成路子图
    /// </summary>
    /// <param name="drawData">开奖数据</param>
    /// <param name="rows">行数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>图片生成结果</returns>
    Task<ImageGenerationResult> GeneratePatternImageAsync(
        KaiJiang drawData, 
        int rows, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送开奖图片
    /// </summary>
    /// <param name="imagePath">图片路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<SendImageResult> SendDrawImageAsync(string imagePath, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取开奖结果
    /// </summary>
    /// <param name="drawData">开奖数据</param>
    /// <param name="betLottery">投注彩种</param>
    /// <returns>开奖结果</returns>
    Task<int> GetDrawResultAsync(KaiJiang drawData, EnumBetLottery betLottery);
    
    /// <summary>
    /// 获取开奖号码
    /// </summary>
    /// <param name="drawData">开奖数据</param>
    /// <param name="betLottery">投注彩种</param>
    /// <returns>开奖号码</returns>
    Task<string> GetDrawNumberAsync(KaiJiang drawData, EnumBetLottery betLottery);
    
    /// <summary>
    /// 判断项目中奖情况
    /// </summary>
    /// <param name="betContent">投注内容</param>
    /// <param name="drawResult">开奖结果</param>
    /// <returns>中奖情况</returns>
    Task<EnumBetWinLose> JudgeWinLoseAsync(string betContent, int drawResult);
}

/// <summary>
/// 开奖信息获取结果
/// </summary>
public class DrawInfoResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<KaiJiang> NewDrawData { get; set; } = new();
    public int NewCount { get; set; }
}

/// <summary>
/// 开奖信息处理结果
/// </summary>
public class ProcessDrawResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public bool HasNewDraw { get; set; }
    public KaiJiang? LatestDraw { get; set; }
    public List<string> GeneratedImages { get; set; } = new();
}

/// <summary>
/// 图片生成结果
/// </summary>
public class ImageGenerationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string ImagePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
}

/// <summary>
/// 发送图片结果
/// </summary>
public class SendImageResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime SentTime { get; set; }
}
