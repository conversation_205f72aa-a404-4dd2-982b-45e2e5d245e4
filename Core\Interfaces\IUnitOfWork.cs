namespace Robot.Core.Interfaces;

/// <summary>
/// 工作单元接口
/// 用于管理事务和数据一致性
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// 获取指定类型的仓储
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>仓储实例</returns>
    IRepository<T> Repository<T>() where T : class;
    
    /// <summary>
    /// 开始事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务任务</returns>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 提交事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>提交任务</returns>
    Task CommitAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 回滚事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>回滚任务</returns>
    Task RollbackAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
